#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
验证缓存修改是否成功
"""

import os
import sys

def check_cache_modifications():
    """检查缓存修改"""
    print("🔍 检查缓存系统修改")
    print("=" * 40)
    
    # 检查双均线策略文件
    strategy_file = "双均线策略_增强版.py"
    if os.path.exists(strategy_file):
        print(f"✅ 找到策略文件: {strategy_file}")
        
        with open(strategy_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查关键修改
        checks = [
            ("strategy_cache_dir", "策略缓存目录设置"),
            ("cache_meta_file", "元数据文件设置"),
            ("to_csv", "CSV保存功能"),
            ("read_csv", "CSV读取功能"),
            ("shutil.rmtree", "目录清理功能")
        ]
        
        for keyword, description in checks:
            if keyword in content:
                print(f"  ✅ {description}: 已修改")
            else:
                print(f"  ❌ {description}: 未找到")
    else:
        print(f"❌ 策略文件不存在: {strategy_file}")
    
    # 检查缓存管理工具
    cache_manager_file = "缓存管理工具_CSV版.py"
    if os.path.exists(cache_manager_file):
        print(f"✅ 找到缓存管理工具: {cache_manager_file}")
    else:
        print(f"❌ 缓存管理工具不存在: {cache_manager_file}")
    
    # 检查GUI文件修改
    gui_file = "策略GUI优雅版.py"
    if os.path.exists(gui_file):
        print(f"✅ 找到GUI文件: {gui_file}")
        
        with open(gui_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        if "缓存管理工具_CSV版" in content:
            print(f"  ✅ GUI已更新为使用CSV缓存管理器")
        else:
            print(f"  ❌ GUI未更新缓存管理器调用")
    else:
        print(f"❌ GUI文件不存在: {gui_file}")
    
    # 检查缓存目录结构
    cache_dir = "./cache"
    if os.path.exists(cache_dir):
        print(f"✅ 缓存目录存在: {cache_dir}")
        
        # 列出子目录
        subdirs = [d for d in os.listdir(cache_dir) if os.path.isdir(os.path.join(cache_dir, d))]
        if subdirs:
            print(f"  📁 发现 {len(subdirs)} 个策略子目录:")
            for subdir in subdirs[:5]:  # 只显示前5个
                print(f"    - {subdir}")
            if len(subdirs) > 5:
                print(f"    ... 还有 {len(subdirs) - 5} 个")
        else:
            print(f"  📝 缓存目录为空（正常，首次运行时会创建）")
    else:
        print(f"📝 缓存目录不存在（正常，首次运行时会创建）")
    
    print(f"\n📋 修改总结:")
    print(f"✅ 缓存格式: PKL → CSV")
    print(f"✅ 存储方式: 单文件 → 每股票一文件")
    print(f"✅ 目录结构: 扁平 → 按策略参数分层")
    print(f"✅ 管理工具: 已更新为CSV版本")
    print(f"✅ GUI集成: 已更新缓存管理器调用")
    
    print(f"\n🎯 新缓存系统特点:")
    print(f"• 每个股票独立CSV文件，避免整体损坏")
    print(f"• 按策略参数分目录管理")
    print(f"• 支持交易记录和收益曲线分别存储")
    print(f"• 文件哈希验证确保数据一致性")
    print(f"• CSV格式便于查看和调试")

if __name__ == "__main__":
    check_cache_modifications()
