#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
缓存管理工具 - CSV版本
用于管理新的CSV格式缓存文件
"""

import os
import glob
import pandas as pd
import shutil
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
from datetime import datetime

class CSVCacheManagerWindow:
    def __init__(self, parent=None):
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.window.title("缓存管理工具 - CSV版")
        self.window.geometry("900x700")

        self.cache_dir = "./cache"
        self.backup_dir = "./cache_backup"

        # 缓存统计信息
        self.cache_stats = {}

        self.create_widgets()
        self.scan_cache_directories()

    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="CSV缓存管理工具",
                               style='Title.TLabel', font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 10))

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(button_frame, text="🔍 扫描缓存",
                  command=self.scan_cache_directories).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame, text="💾 备份缓存",
                  command=self.backup_cache).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame, text="🗑️ 清理空目录",
                  command=self.clean_empty_directories).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame, text="🧹 清空所有缓存",
                  command=self.clear_all_cache).pack(side=tk.LEFT, padx=(0, 10))

        # 状态标签
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var)
        status_label.pack(pady=(0, 10))

        # 缓存目录列表
        list_frame = ttk.LabelFrame(main_frame, text="缓存目录", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 创建Treeview
        columns = ('目录名', '策略参数', '股票数量', '总大小', '最后更新')
        self.cache_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)

        for col in columns:
            self.cache_tree.heading(col, text=col)
            self.cache_tree.column(col, width=120, anchor='center')

        # 滚动条
        scrollbar_v = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.cache_tree.yview)
        scrollbar_h = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.cache_tree.xview)
        self.cache_tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)

        self.cache_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)

        # 右键菜单
        self.create_context_menu()

        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, width=80)
        self.log_text.pack(fill=tk.BOTH, expand=True)

    def create_context_menu(self):
        """创建右键菜单"""
        self.context_menu = tk.Menu(self.window, tearoff=0)
        self.context_menu.add_command(label="📊 查看详情", command=self.view_cache_details)
        self.context_menu.add_command(label="🗑️ 删除该缓存", command=self.delete_selected_cache)
        self.context_menu.add_command(label="📁 打开目录", command=self.open_cache_directory)

        self.cache_tree.bind("<Button-3>", self.show_context_menu)

    def show_context_menu(self, event):
        """显示右键菜单"""
        selection = self.cache_tree.selection()
        if selection:
            self.context_menu.post(event.x_root, event.y_root)

    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.window.update_idletasks()

    def scan_cache_directories(self):
        """扫描缓存目录"""
        self.log_message("🔍 开始扫描缓存目录...")

        # 清空现有数据
        for item in self.cache_tree.get_children():
            self.cache_tree.delete(item)

        self.cache_stats.clear()

        if not os.path.exists(self.cache_dir):
            self.log_message("❌ 缓存目录不存在")
            self.status_var.set("缓存目录不存在")
            return

        # 扫描所有策略子目录
        strategy_dirs = [d for d in os.listdir(self.cache_dir)
                        if os.path.isdir(os.path.join(self.cache_dir, d))]

        total_dirs = 0
        total_stocks = 0
        total_size = 0

        for strategy_dir in strategy_dirs:
            strategy_path = os.path.join(self.cache_dir, strategy_dir)

            try:
                # 统计该策略目录的信息
                csv_files = glob.glob(os.path.join(strategy_path, "*.csv"))

                # 排除元数据文件，只计算股票文件
                stock_files = [f for f in csv_files if not f.endswith('cache_meta.csv')]
                stock_count = len([f for f in stock_files if not ('_trades.csv' in f or '_equity.csv' in f)])

                # 计算目录大小
                dir_size = 0
                for file_path in csv_files:
                    try:
                        dir_size += os.path.getsize(file_path)
                    except:
                        pass

                # 获取最后修改时间
                try:
                    meta_file = os.path.join(strategy_path, "cache_meta.csv")
                    if os.path.exists(meta_file):
                        last_modified = datetime.fromtimestamp(os.path.getmtime(meta_file))
                        last_modified_str = last_modified.strftime("%Y-%m-%d %H:%M")

                        # 读取策略参数
                        meta_df = pd.read_csv(meta_file, encoding='utf-8')
                        if not meta_df.empty:
                            strategy_params = f"MA{meta_df['short_window'].iloc[0]}-MA{meta_df['long_window'].iloc[0]}_{meta_df['execution_price'].iloc[0]}"
                        else:
                            strategy_params = "未知参数"
                    else:
                        last_modified_str = "未知"
                        strategy_params = "无元数据"
                except Exception as e:
                    last_modified_str = "错误"
                    strategy_params = "读取失败"

                # 格式化大小
                if dir_size > 1024*1024:
                    size_str = f"{dir_size/(1024*1024):.1f} MB"
                elif dir_size > 1024:
                    size_str = f"{dir_size/1024:.1f} KB"
                else:
                    size_str = f"{dir_size} B"

                # 添加到树形视图
                self.cache_tree.insert('', 'end', values=(
                    strategy_dir,
                    strategy_params,
                    stock_count,
                    size_str,
                    last_modified_str
                ))

                # 统计信息
                self.cache_stats[strategy_dir] = {
                    'path': strategy_path,
                    'stock_count': stock_count,
                    'size': dir_size,
                    'params': strategy_params,
                    'last_modified': last_modified_str
                }

                total_dirs += 1
                total_stocks += stock_count
                total_size += dir_size

                self.log_message(f"  ✓ {strategy_dir}: {stock_count} 只股票, {size_str}")

            except Exception as e:
                self.log_message(f"  ❌ 扫描 {strategy_dir} 失败: {str(e)}")

        # 更新状态
        total_size_str = f"{total_size/(1024*1024):.1f} MB" if total_size > 1024*1024 else f"{total_size/1024:.1f} KB"
        self.status_var.set(f"找到 {total_dirs} 个策略缓存，共 {total_stocks} 只股票，总大小 {total_size_str}")
        self.log_message(f"✅ 扫描完成: {total_dirs} 个策略目录，{total_stocks} 只股票缓存")

    def backup_cache(self):
        """备份缓存"""
        if not os.path.exists(self.cache_dir):
            messagebox.showwarning("警告", "缓存目录不存在")
            return

        # 创建带时间戳的备份目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"{self.backup_dir}_{timestamp}"

        try:
            self.log_message(f"💾 开始备份缓存到: {backup_path}")
            shutil.copytree(self.cache_dir, backup_path)

            # 计算备份大小
            backup_size = 0
            for root, dirs, files in os.walk(backup_path):
                for file in files:
                    backup_size += os.path.getsize(os.path.join(root, file))

            size_str = f"{backup_size/(1024*1024):.1f} MB" if backup_size > 1024*1024 else f"{backup_size/1024:.1f} KB"

            self.log_message(f"✅ 备份完成: {backup_path}")
            self.log_message(f"   备份大小: {size_str}")
            self.status_var.set(f"备份完成: {size_str}")

            messagebox.showinfo("备份完成", f"缓存已备份到:\n{backup_path}\n\n备份大小: {size_str}")

        except Exception as e:
            self.log_message(f"❌ 备份失败: {str(e)}")
            messagebox.showerror("备份失败", f"备份过程中出错:\n{str(e)}")

    def clean_empty_directories(self):
        """清理空目录"""
        if not os.path.exists(self.cache_dir):
            return

        self.log_message("🧹 开始清理空目录...")
        cleaned_count = 0

        for strategy_dir in os.listdir(self.cache_dir):
            strategy_path = os.path.join(self.cache_dir, strategy_dir)

            if os.path.isdir(strategy_path):
                try:
                    # 检查目录是否为空或只包含无效文件
                    files = os.listdir(strategy_path)
                    if not files:
                        # 完全空目录
                        os.rmdir(strategy_path)
                        self.log_message(f"  🗑️ 删除空目录: {strategy_dir}")
                        cleaned_count += 1
                    else:
                        # 检查是否只有无效文件
                        valid_files = [f for f in files if f.endswith('.csv')]
                        if not valid_files:
                            shutil.rmtree(strategy_path)
                            self.log_message(f"  🗑️ 删除无效目录: {strategy_dir}")
                            cleaned_count += 1
                except Exception as e:
                    self.log_message(f"  ❌ 清理 {strategy_dir} 失败: {str(e)}")

        self.log_message(f"✅ 清理完成，删除了 {cleaned_count} 个空目录")
        self.status_var.set(f"清理了 {cleaned_count} 个空目录")

        # 重新扫描
        self.scan_cache_directories()

    def clear_all_cache(self):
        """清空所有缓存"""
        if not os.path.exists(self.cache_dir):
            messagebox.showinfo("提示", "缓存目录不存在")
            return

        # 统计要删除的内容
        total_dirs = len([d for d in os.listdir(self.cache_dir)
                         if os.path.isdir(os.path.join(self.cache_dir, d))])

        if total_dirs == 0:
            messagebox.showinfo("提示", "缓存目录已经为空")
            return

        confirm = messagebox.askyesno("确认清空",
                                     f"确定要删除所有 {total_dirs} 个策略缓存吗？\n"
                                     "删除后所有策略都需要重新计算。\n"
                                     "建议先进行备份。")
        if not confirm:
            return

        self.log_message(f"🧹 开始清空所有缓存...")

        deleted_count = 0
        for strategy_dir in os.listdir(self.cache_dir):
            strategy_path = os.path.join(self.cache_dir, strategy_dir)
            if os.path.isdir(strategy_path):
                try:
                    shutil.rmtree(strategy_path)
                    self.log_message(f"   ✅ 已删除: {strategy_dir}")
                    deleted_count += 1
                except Exception as e:
                    self.log_message(f"   ❌ 删除失败: {strategy_dir} - {str(e)}")

        self.log_message(f"✅ 清空完成，共删除 {deleted_count} 个策略缓存")
        self.status_var.set(f"已清空 {deleted_count} 个策略缓存")

        # 重新扫描
        self.scan_cache_directories()

    def view_cache_details(self):
        """查看缓存详情"""
        selection = self.cache_tree.selection()
        if not selection:
            return

        item = selection[0]
        strategy_dir = self.cache_tree.item(item)['values'][0]

        if strategy_dir not in self.cache_stats:
            messagebox.showerror("错误", "缓存信息不存在")
            return

        stats = self.cache_stats[strategy_dir]
        strategy_path = stats['path']

        # 创建详情窗口
        detail_window = tk.Toplevel(self.window)
        detail_window.title(f"缓存详情 - {strategy_dir}")
        detail_window.geometry("800x600")

        # 基本信息
        info_frame = ttk.LabelFrame(detail_window, text="基本信息", padding="10")
        info_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(info_frame, text=f"目录名: {strategy_dir}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"策略参数: {stats['params']}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"股票数量: {stats['stock_count']}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"目录大小: {stats['size']/(1024*1024):.2f} MB").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"最后更新: {stats['last_modified']}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"完整路径: {strategy_path}").pack(anchor=tk.W)

        # 文件列表
        files_frame = ttk.LabelFrame(detail_window, text="文件列表", padding="10")
        files_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 创建文件列表
        columns = ('文件名', '类型', '大小', '修改时间')
        files_tree = ttk.Treeview(files_frame, columns=columns, show='headings', height=15)

        for col in columns:
            files_tree.heading(col, text=col)
            files_tree.column(col, width=150, anchor='center')

        # 滚动条
        files_scrollbar = ttk.Scrollbar(files_frame, orient=tk.VERTICAL, command=files_tree.yview)
        files_tree.configure(yscrollcommand=files_scrollbar.set)

        files_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        files_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 填充文件信息
        try:
            csv_files = glob.glob(os.path.join(strategy_path, "*.csv"))
            for file_path in sorted(csv_files):
                filename = os.path.basename(file_path)
                file_size = os.path.getsize(file_path)
                file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))

                # 确定文件类型
                if filename == 'cache_meta.csv':
                    file_type = '元数据'
                elif filename.endswith('_trades.csv'):
                    file_type = '交易记录'
                elif filename.endswith('_equity.csv'):
                    file_type = '收益曲线'
                else:
                    file_type = '基本结果'

                # 格式化大小
                if file_size > 1024:
                    size_str = f"{file_size/1024:.1f} KB"
                else:
                    size_str = f"{file_size} B"

                files_tree.insert('', 'end', values=(
                    filename,
                    file_type,
                    size_str,
                    file_mtime.strftime("%Y-%m-%d %H:%M:%S")
                ))
        except Exception as e:
            ttk.Label(files_frame, text=f"读取文件列表失败: {str(e)}").pack()

    def delete_selected_cache(self):
        """删除选中的缓存"""
        selection = self.cache_tree.selection()
        if not selection:
            return

        item = selection[0]
        strategy_dir = self.cache_tree.item(item)['values'][0]

        if strategy_dir not in self.cache_stats:
            messagebox.showerror("错误", "缓存信息不存在")
            return

        stats = self.cache_stats[strategy_dir]

        confirm = messagebox.askyesno("确认删除",
                                     f"确定要删除策略缓存吗？\n\n"
                                     f"策略: {stats['params']}\n"
                                     f"股票数量: {stats['stock_count']}\n"
                                     f"删除后该策略需要重新计算。")
        if not confirm:
            return

        try:
            strategy_path = stats['path']
            shutil.rmtree(strategy_path)

            self.log_message(f"🗑️ 已删除策略缓存: {strategy_dir}")
            self.status_var.set(f"已删除: {strategy_dir}")

            # 重新扫描
            self.scan_cache_directories()

        except Exception as e:
            self.log_message(f"❌ 删除失败: {str(e)}")
            messagebox.showerror("删除失败", f"删除缓存时出错:\n{str(e)}")

    def open_cache_directory(self):
        """打开缓存目录"""
        selection = self.cache_tree.selection()
        if not selection:
            return

        item = selection[0]
        strategy_dir = self.cache_tree.item(item)['values'][0]

        if strategy_dir not in self.cache_stats:
            messagebox.showerror("错误", "缓存信息不存在")
            return

        strategy_path = self.cache_stats[strategy_dir]['path']

        try:
            import subprocess
            import sys

            if sys.platform.startswith('win'):
                os.startfile(strategy_path)
            elif sys.platform.startswith('darwin'):
                subprocess.run(['open', strategy_path])
            else:
                subprocess.run(['xdg-open', strategy_path])

        except Exception as e:
            messagebox.showerror("错误", f"无法打开目录:\n{str(e)}")


def main():
    """主函数"""
    app = CSVCacheManagerWindow()
    app.window.mainloop()


if __name__ == "__main__":
    main()
