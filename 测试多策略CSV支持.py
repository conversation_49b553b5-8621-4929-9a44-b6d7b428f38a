#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试多策略比较功能的CSV支持
验证新的CSV缓存格式是否能被多策略比较工具正确识别和加载
"""

import os
import sys
import glob

def test_csv_strategy_detection():
    """测试CSV策略检测功能"""
    print("🧪 测试多策略比较的CSV支持")
    print("=" * 50)
    
    try:
        # 检查缓存目录
        cache_dir = "./cache"
        if not os.path.exists(cache_dir):
            print(f"❌ 缓存目录不存在: {cache_dir}")
            return
        
        print(f"✅ 缓存目录存在: {cache_dir}")
        
        # 扫描CSV格式的策略目录
        strategy_dirs = [d for d in os.listdir(cache_dir) 
                        if os.path.isdir(os.path.join(cache_dir, d))]
        
        if not strategy_dirs:
            print(f"📝 未找到CSV策略目录")
            print(f"   建议先运行策略生成一些CSV缓存")
            return
        
        print(f"📁 找到 {len(strategy_dirs)} 个策略目录:")
        
        csv_strategies = []
        for strategy_dir in strategy_dirs:
            strategy_path = os.path.join(cache_dir, strategy_dir)
            meta_file = os.path.join(strategy_path, "cache_meta.csv")
            
            print(f"\n📊 分析策略目录: {strategy_dir}")
            print(f"   路径: {strategy_path}")
            
            if os.path.exists(meta_file):
                print(f"   ✅ 元数据文件存在")
                
                try:
                    import pandas as pd
                    meta_df = pd.read_csv(meta_file, encoding='utf-8')
                    
                    if not meta_df.empty:
                        short_window = meta_df['short_window'].iloc[0]
                        long_window = meta_df['long_window'].iloc[0]
                        execution_price = meta_df['execution_price'].iloc[0]
                        
                        print(f"   📋 策略参数: MA{short_window}-MA{long_window}_{execution_price}")
                        print(f"   📋 股票数量: {len(meta_df)}")
                        
                        # 检查收益率曲线文件
                        equity_files = glob.glob(os.path.join(strategy_path, "*_equity.csv"))
                        print(f"   📈 收益率曲线文件: {len(equity_files)} 个")
                        
                        if equity_files:
                            # 检查第一个文件的内容
                            sample_file = equity_files[0]
                            sample_df = pd.read_csv(sample_file, encoding='utf-8')
                            print(f"   📊 样本文件: {os.path.basename(sample_file)}")
                            print(f"   📊 数据点数: {len(sample_df)}")
                            print(f"   📊 列名: {list(sample_df.columns)}")
                            
                            csv_strategies.append({
                                'id': strategy_dir,
                                'name': f"MA{short_window}-MA{long_window}({execution_price})",
                                'path': strategy_path,
                                'stocks': len(meta_df),
                                'equity_files': len(equity_files),
                                'data_points': len(sample_df)
                            })
                        else:
                            print(f"   ⚠️ 未找到收益率曲线文件")
                    else:
                        print(f"   ❌ 元数据文件为空")
                        
                except Exception as e:
                    print(f"   ❌ 读取元数据失败: {str(e)}")
            else:
                print(f"   ❌ 缺少元数据文件")
        
        print(f"\n📋 CSV策略总结:")
        print(f"   有效策略: {len(csv_strategies)} 个")
        
        for strategy in csv_strategies:
            print(f"   • {strategy['name']}: {strategy['stocks']}只股票, {strategy['data_points']}个数据点")
        
        # 测试多策略比较工具的导入
        print(f"\n🛠️ 测试多策略比较工具...")
        
        try:
            from 多策略收益率对比_智能对齐GUI import MultiStrategyComparisonWindow
            print(f"✅ 多策略比较工具导入成功")
            
            # 不启动GUI，只测试类的创建
            print(f"   主要功能:")
            print(f"   - 自动检测CSV和PKL格式策略")
            print(f"   - 支持CSV收益率曲线加载")
            print(f"   - 智能对齐分析")
            print(f"   - 图表绘制和数据导出")
            
        except ImportError as e:
            print(f"❌ 多策略比较工具导入失败: {str(e)}")
        
        # 检查PKL格式兼容性
        print(f"\n🔄 检查PKL格式兼容性...")
        pkl_files = glob.glob(os.path.join(cache_dir, "strategy_cache_*.pkl"))
        
        if pkl_files:
            print(f"📁 找到 {len(pkl_files)} 个PKL缓存文件")
            print(f"   多策略比较工具支持同时使用CSV和PKL格式")
        else:
            print(f"📝 未找到PKL缓存文件")
            print(f"   当前只有CSV格式缓存")
        
        print(f"\n✅ CSV支持测试完成!")
        
        if csv_strategies:
            print(f"🎉 发现 {len(csv_strategies)} 个可用的CSV策略")
            print(f"📊 可以使用多策略比较工具进行分析")
            print(f"\n启动方法:")
            print(f"python 多策略收益率对比_智能对齐GUI.py")
        else:
            print(f"⚠️ 未找到可用的CSV策略")
            print(f"   建议先运行策略生成一些缓存数据")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()


def show_csv_vs_pkl_comparison():
    """显示CSV和PKL格式的对比"""
    print(f"\n📊 CSV vs PKL 格式对比")
    print("=" * 40)
    
    print(f"PKL格式 (旧):")
    print(f"✅ 文件紧凑，读写速度快")
    print(f"❌ 二进制格式，无法直接查看")
    print(f"❌ 整个文件损坏会丢失所有数据")
    print(f"❌ 版本兼容性问题")
    
    print(f"\nCSV格式 (新):")
    print(f"✅ 文本格式，可用Excel等工具查看")
    print(f"✅ 每个股票独立文件，局部损坏不影响其他")
    print(f"✅ 按策略参数分目录，便于管理")
    print(f"✅ 支持增量更新和验证")
    print(f"✅ 跨平台兼容性好")
    print(f"❌ 文件数量多，占用空间稍大")
    
    print(f"\n🔄 兼容性:")
    print(f"• 多策略比较工具同时支持两种格式")
    print(f"• 优先使用CSV格式，自动回退到PKL")
    print(f"• 可以混合使用不同格式的策略")


if __name__ == "__main__":
    print("多策略比较CSV支持测试程序")
    print("=" * 50)
    
    # 显示格式对比
    show_csv_vs_pkl_comparison()
    
    # 测试CSV策略检测
    test_csv_strategy_detection()
    
    print(f"\n🎉 测试程序完成!")
