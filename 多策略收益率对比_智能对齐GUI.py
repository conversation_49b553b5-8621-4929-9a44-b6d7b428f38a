#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
多策略收益率对比GUI_智能对齐版
按每天的可用股票数动态计算平均收益率，充分利用所有数据
提供图形化界面用于策略GUI优雅版调用
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pickle
import os
import glob
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import random
from datetime import datetime
import threading
from collections import defaultdict

# 设置matplotlib中文显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class MultiStrategyComparisonWindow:
    """多策略对比窗口 - 智能对齐版"""

    def __init__(self, parent):
        self.parent = parent
        self.window = tk.Toplevel(parent)
        self.window.title("多策略对比分析 - 智能对齐版（动态平均）")
        self.window.geometry("1200x800")

        # 数据存储
        self.strategy_data = {}
        self.benchmark_data = None

        # 创建界面
        self.setup_widgets()

        # 自动加载可用策略
        self.load_available_strategies()

    def setup_widgets(self):
        """设置界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 左侧控制面板
        self.create_control_panel(main_frame)

        # 右侧图表区域
        self.create_chart_area(main_frame)

    def create_control_panel(self, parent):
        """创建左侧控制面板"""
        control_frame = ttk.Frame(parent)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))

        # 策略选择区域
        strategy_frame = ttk.LabelFrame(control_frame, text="策略选择", padding="10")
        strategy_frame.pack(fill=tk.X, pady=(0, 10))

        # 策略列表
        ttk.Label(strategy_frame, text="可用策略:").pack(anchor=tk.W)

        # 创建带滚动条的策略选择区域
        strategy_scroll_frame = ttk.Frame(strategy_frame)
        strategy_scroll_frame.pack(fill=tk.X, pady=(5, 0))

        # 创建Canvas和滚动条
        self.strategy_canvas = tk.Canvas(strategy_scroll_frame, height=120)  # 限制高度为120像素
        strategy_scrollbar = ttk.Scrollbar(strategy_scroll_frame, orient="vertical", command=self.strategy_canvas.yview)
        self.strategy_scrollable_frame = ttk.Frame(self.strategy_canvas)

        # 配置滚动
        self.strategy_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.strategy_canvas.configure(scrollregion=self.strategy_canvas.bbox("all"))
        )

        self.strategy_canvas.create_window((0, 0), window=self.strategy_scrollable_frame, anchor="nw")
        self.strategy_canvas.configure(yscrollcommand=strategy_scrollbar.set)

        # 布局Canvas和滚动条
        self.strategy_canvas.pack(side="left", fill="both", expand=True)
        strategy_scrollbar.pack(side="right", fill="y")

        # 策略复选框将添加到scrollable_frame中
        self.strategy_checks_frame = self.strategy_scrollable_frame

        self.strategy_vars = {}  # 存储策略选择变量

        # 绑定鼠标滚轮事件
        def _on_mousewheel(event):
            self.strategy_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        self.strategy_canvas.bind("<MouseWheel>", _on_mousewheel)

        # 绑定鼠标进入和离开事件，确保滚轮只在策略区域生效
        def _bind_to_mousewheel(event):
            self.strategy_canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_from_mousewheel(event):
            self.strategy_canvas.unbind_all("<MouseWheel>")

        self.strategy_canvas.bind('<Enter>', _bind_to_mousewheel)
        self.strategy_canvas.bind('<Leave>', _unbind_from_mousewheel)

        # 参数设置区域
        params_frame = ttk.LabelFrame(control_frame, text="智能对齐参数", padding="10")
        params_frame.pack(fill=tk.X, pady=(0, 10))

        # 股票数量设置
        ttk.Label(params_frame, text="每策略股票数量:").pack(anchor=tk.W)
        self.num_stocks_var = tk.StringVar(value="50")
        stocks_frame = ttk.Frame(params_frame)
        stocks_frame.pack(fill=tk.X, pady=(5, 10))
        ttk.Entry(stocks_frame, textvariable=self.num_stocks_var, width=10).pack(side=tk.LEFT)
        ttk.Label(stocks_frame, text="只股票").pack(side=tk.LEFT, padx=(5, 0))

        # 最小数据长度设置
        ttk.Label(params_frame, text="最小数据长度:").pack(anchor=tk.W)
        self.min_data_length_var = tk.StringVar(value="3000")
        length_frame = ttk.Frame(params_frame)
        length_frame.pack(fill=tk.X, pady=(5, 10))
        ttk.Entry(length_frame, textvariable=self.min_data_length_var, width=10).pack(side=tk.LEFT)
        ttk.Label(length_frame, text="天（约12年）").pack(side=tk.LEFT, padx=(5, 0))

        # 随机种子设置
        ttk.Label(params_frame, text="随机种子:").pack(anchor=tk.W)
        self.seed_var = tk.StringVar(value="42")
        seed_frame = ttk.Frame(params_frame)
        seed_frame.pack(fill=tk.X, pady=(5, 10))
        ttk.Entry(seed_frame, textvariable=self.seed_var, width=10).pack(side=tk.LEFT)
        ttk.Label(seed_frame, text="(确保可重现)").pack(side=tk.LEFT, padx=(5, 0))

        # 智能对齐说明
        info_frame = ttk.Frame(params_frame)
        info_frame.pack(fill=tk.X, pady=(5, 0))
        info_label = ttk.Label(info_frame, text="💡 智能对齐：每天按可用股票数动态平均",
                              foreground="blue", font=("Arial", 9))
        info_label.pack(anchor=tk.W)

        # 基准指数选项
        self.include_benchmark_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(params_frame, text="包含上证指数基准",
                       variable=self.include_benchmark_var).pack(anchor=tk.W)

        # 操作按钮区域
        button_frame = ttk.LabelFrame(control_frame, text="操作", padding="10")
        button_frame.pack(fill=tk.X, pady=(0, 10))

        # 修复缓存按钮
        ttk.Button(button_frame, text="🔧 修复缓存文件",
                  command=self.repair_cache).pack(fill=tk.X, pady=(0, 5))

        # 分析按钮
        ttk.Button(button_frame, text="🔍 开始智能对齐分析",
                  command=self.start_analysis).pack(fill=tk.X, pady=(0, 5))

        # 保存图表按钮
        ttk.Button(button_frame, text="💾 保存图表",
                  command=self.save_chart).pack(fill=tk.X, pady=(0, 5))

        # 导出数据按钮
        ttk.Button(button_frame, text="📊 导出数据",
                  command=self.export_data).pack(fill=tk.X, pady=(0, 5))

        # 状态显示区域
        status_frame = ttk.LabelFrame(control_frame, text="状态", padding="10")
        status_frame.pack(fill=tk.BOTH, expand=True)

        # 状态文本
        self.status_text = tk.Text(status_frame, height=8, wrap=tk.WORD)
        status_scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=status_scrollbar.set)

        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        status_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_chart_area(self, parent):
        """创建右侧图表区域"""
        chart_frame = ttk.Frame(parent)
        chart_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 创建matplotlib图表
        self.fig, self.ax = plt.subplots(figsize=(10, 6))
        self.fig.patch.set_facecolor('white')

        # 创建画布
        self.canvas = FigureCanvasTkAgg(self.fig, chart_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # 创建工具栏
        toolbar = NavigationToolbar2Tk(self.canvas, chart_frame)
        toolbar.update()

    def log_status(self, message):
        """记录状态信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"

        self.status_text.insert(tk.END, full_message)
        self.status_text.see(tk.END)
        self.window.update()

    def safe_load_pickle(self, pkl_file):
        """安全加载pickle文件，处理损坏的文件"""
        try:
            # 首先检查文件大小
            file_size = os.path.getsize(pkl_file)
            if file_size == 0:
                self.log_status(f"⚠️ 文件为空: {os.path.basename(pkl_file)}")
                return None

            # 尝试加载pickle文件
            with open(pkl_file, 'rb') as f:
                cache_data = pickle.load(f)

            # 验证数据结构
            if not isinstance(cache_data, dict):
                self.log_status(f"⚠️ 缓存数据格式错误: {os.path.basename(pkl_file)}")
                return None

            return cache_data

        except EOFError:
            self.log_status(f"❌ 文件损坏(EOFError): {os.path.basename(pkl_file)}")
            return None
        except pickle.UnpicklingError as e:
            self.log_status(f"❌ 文件损坏(UnpicklingError): {os.path.basename(pkl_file)} - {str(e)}")
            return None
        except Exception as e:
            self.log_status(f"❌ 加载失败: {os.path.basename(pkl_file)} - {str(e)}")
            return None

    def repair_cache(self):
        """修复缓存文件 - 检测并删除损坏的文件"""
        self.log_status("🔧 开始缓存文件修复...")

        cache_dir = "./cache"
        if not os.path.exists(cache_dir):
            self.log_status("❌ 缓存目录不存在")
            messagebox.showinfo("提示", "缓存目录不存在")
            return

        cache_files = glob.glob(os.path.join(cache_dir, "*.pkl"))
        if not cache_files:
            self.log_status("📝 缓存目录为空")
            messagebox.showinfo("提示", "缓存目录为空")
            return

        self.log_status(f"📁 找到 {len(cache_files)} 个缓存文件，开始检查...")

        valid_files = []
        corrupted_files = []

        for pkl_file in cache_files:
            filename = os.path.basename(pkl_file)
            file_size = os.path.getsize(pkl_file)

            self.log_status(f"📄 检查: {filename} ({file_size:,} 字节)")

            if file_size == 0:
                self.log_status(f"   ❌ 文件为空")
                corrupted_files.append(pkl_file)
                continue

            cache_data = self.safe_load_pickle(pkl_file)
            if cache_data is None:
                corrupted_files.append(pkl_file)
            else:
                stock_count = len(cache_data)
                self.log_status(f"   ✅ 文件正常，包含 {stock_count} 只股票数据")
                valid_files.append(pkl_file)

        # 显示检查结果
        self.log_status(f"\n📊 检查结果:")
        self.log_status(f"   ✅ 正常文件: {len(valid_files)} 个")
        self.log_status(f"   ❌ 损坏文件: {len(corrupted_files)} 个")

        if corrupted_files:
            self.log_status(f"\n🚨 损坏的文件:")
            for f in corrupted_files:
                self.log_status(f"   - {os.path.basename(f)}")

            # 询问是否删除
            result = messagebox.askyesno("确认删除",
                                       f"发现 {len(corrupted_files)} 个损坏的缓存文件。\n"
                                       f"是否删除这些文件？\n\n"
                                       f"删除后相应的策略需要重新计算。")

            if result:
                deleted_count = 0
                for pkl_file in corrupted_files:
                    try:
                        os.remove(pkl_file)
                        self.log_status(f"   ✅ 已删除: {os.path.basename(pkl_file)}")
                        deleted_count += 1
                    except Exception as e:
                        self.log_status(f"   ❌ 删除失败: {os.path.basename(pkl_file)} - {str(e)}")

                self.log_status(f"\n✅ 修复完成，删除了 {deleted_count} 个损坏文件")
                messagebox.showinfo("修复完成", f"成功删除 {deleted_count} 个损坏文件")

                # 重新加载策略列表
                self.load_available_strategies()
            else:
                self.log_status("\n📝 已取消删除操作")
        else:
            self.log_status("\n🎉 所有缓存文件都正常！")
            messagebox.showinfo("检查完成", "所有缓存文件都正常，无需修复")

    def load_available_strategies(self):
        """加载可用的策略缓存文件 - 支持CSV格式"""
        self.log_status("🔍 扫描可用策略缓存文件...")

        cache_dir = "./cache"
        if not os.path.exists(cache_dir):
            self.log_status("❌ 缓存目录不存在")
            return

        # 优先查找CSV格式的策略目录
        strategy_dirs = [d for d in os.listdir(cache_dir)
                        if os.path.isdir(os.path.join(cache_dir, d))]

        if strategy_dirs:
            self.log_status(f"📁 找到CSV格式策略目录: {len(strategy_dirs)} 个")
            self._load_csv_strategies(strategy_dirs)
        else:
            # 回退到PKL格式
            pkl_files = glob.glob(os.path.join(cache_dir, "strategy_cache_*.pkl"))
            self.log_status(f"📁 找到PKL缓存文件: {pkl_files}")

            if not pkl_files:
                self.log_status("❌ 未找到策略缓存文件")
                return
            else:
                self._load_pkl_strategies(pkl_files)

    def _load_csv_strategies(self, strategy_dirs):
        """加载CSV格式的策略"""
        self.log_status("📊 分析CSV格式策略...")

        # 清除现有复选框
        for widget in self.strategy_checks_frame.winfo_children():
            widget.destroy()
        self.strategy_vars.clear()

        # 重置滚动区域
        self.strategy_canvas.configure(scrollregion=(0, 0, 0, 0))

        strategy_info = {}
        cache_dir = "./cache"

        for strategy_dir in strategy_dirs:
            try:
                strategy_path = os.path.join(cache_dir, strategy_dir)
                meta_file = os.path.join(strategy_path, "cache_meta.csv")

                if not os.path.exists(meta_file):
                    self.log_status(f"⚠️ 策略目录缺少元数据文件: {strategy_dir}")
                    continue

                # 读取元数据
                meta_df = pd.read_csv(meta_file, encoding='utf-8')
                if meta_df.empty:
                    self.log_status(f"⚠️ 策略元数据为空: {strategy_dir}")
                    continue

                # 获取策略参数
                short_window = meta_df['short_window'].iloc[0]
                long_window = meta_df['long_window'].iloc[0]
                execution_price = meta_df['execution_price'].iloc[0]

                # 统计有效股票数量
                stock_files = [f for f in os.listdir(strategy_path)
                              if f.endswith('.csv') and not f.endswith('_trades.csv')
                              and not f.endswith('_equity.csv') and f != 'cache_meta.csv']
                valid_count = len(stock_files)

                # 创建策略ID和显示名称
                strategy_id = strategy_dir

                # 获取执行价格显示名称
                from 策略配置 import EXECUTION_PRICE_TYPES
                execution_name = EXECUTION_PRICE_TYPES.get(execution_price, {}).get('name', execution_price)

                # 创建完整的策略显示名称
                strategy_name = f"MA{short_window}-MA{long_window}({execution_name})"

                strategy_info[strategy_id] = {
                    'name': strategy_name,
                    'dir': strategy_path,
                    'valid_count': valid_count,
                    'params': {
                        'short_window': int(short_window),
                        'long_window': int(long_window),
                        'execution_price': execution_price
                    },
                    'format': 'csv'
                }

                self.log_status(f"✅ CSV策略: {strategy_name}")
                self.log_status(f"📊 有效股票数量: {valid_count}")

            except Exception as e:
                self.log_status(f"❌ 分析CSV策略失败: {strategy_dir} - {str(e)}")
                continue

        # 存储策略信息并创建复选框
        self.strategy_info = strategy_info
        self._create_strategy_checkboxes(strategy_info)

    def _create_strategy_checkboxes(self, strategy_info):
        """创建策略复选框"""
        if strategy_info:
            for strategy_id, info in sorted(strategy_info.items()):
                var = tk.BooleanVar(value=True)  # 默认选中
                self.strategy_vars[strategy_id] = var
                text = f"{info['name']} ({info['valid_count']}只股票)"
                checkbox = ttk.Checkbutton(self.strategy_checks_frame, text=text, variable=var)
                checkbox.pack(anchor=tk.W, pady=1)
            self.log_status(f"✅ 找到 {len(strategy_info)} 个可用策略")
        else:
            self.log_status("❌ 没有找到有效的策略缓存")

    def _load_pkl_strategies(self, pkl_files):
        """加载PKL格式的策略（向后兼容）"""
        self.log_status("📊 分析PKL格式策略...")

        # 清除现有复选框
        for widget in self.strategy_checks_frame.winfo_children():
            widget.destroy()
        self.strategy_vars.clear()

        # 重置滚动区域
        self.strategy_canvas.configure(scrollregion=(0, 0, 0, 0))

        # 分析每个缓存文件
        strategy_info = {}
        for pkl_file in pkl_files:
            try:
                file_name = os.path.basename(pkl_file)
                strategy_id = file_name.replace("strategy_cache_", "").replace(".pkl", "")
                self.log_status(f"📄 分析缓存文件: {file_name}")
                self.log_status(f"🔑 策略ID: {strategy_id}")

                # 安全检查缓存数据
                cache_data = self.safe_load_pickle(pkl_file)
                if cache_data is None:
                    continue

                self.log_status(f"📊 缓存数据键: {list(cache_data.keys())[:5]}...")

                # 检查第一个股票的数据结构
                first_stock = next(iter(cache_data.values()))
                self.log_status(f"📊 第一个股票数据结构: {type(first_stock)}")
                if isinstance(first_stock, dict):
                    self.log_status(f"📊 第一个股票数据键: {list(first_stock.keys())}")

                if isinstance(first_stock, dict) and 'equity_curve' in first_stock:
                    # 统计有效equity_curve数量
                    valid_count = 0
                    for result in cache_data.values():
                        if (result and isinstance(result, dict) and
                            'equity_curve' in result and result['equity_curve']):
                            valid_count += 1

                    # 从strategy_id中提取MA参数和执行价格
                    try:
                        # 解析策略ID格式: MA3_10_open 或 稳健策略_open
                        parts = strategy_id.split('_')

                        # 提取执行价格（最后一部分）
                        execution_price = 'open'  # 默认值
                        execution_name = '开盘价'  # 默认中文名

                        if len(parts) >= 2:
                            last_part = parts[-1].lower()  # 转换为小写以确保匹配
                            if last_part in ['open', 'close', 'rand', 'random']:  # 添加random作为可能的选项
                                execution_price = 'rand' if last_part in ['rand', 'random'] else last_part
                                execution_name = {
                                    'open': '开盘价',
                                    'close': '收盘价',
                                    'rand': '随机价'
                                }.get(execution_price, '开盘价')
                                # 移除执行价格部分，获取策略名部分
                                strategy_parts = parts[:-1]
                            else:
                                strategy_parts = parts
                        else:
                            strategy_parts = parts

                        # 解析MA参数
                        if len(strategy_parts) >= 2 and strategy_parts[0].startswith('MA'):
                            # 格式: MA3_10
                            ma_part = f"{strategy_parts[0]}_{strategy_parts[1]}"
                            params_str = strategy_parts[0].replace('MA', '')
                            short_window = int(params_str)
                            long_window = int(strategy_parts[1])
                            base_name = f"MA({short_window},{long_window})"
                        elif len(strategy_parts) >= 1:
                            # 格式: 稳健策略 等预设策略名
                            base_name = '_'.join(strategy_parts)
                            # 尝试从预设策略中获取参数
                            from 策略配置 import PRESET_STRATEGIES, get_strategy_config
                            try:
                                config = get_strategy_config(base_name)
                                short_window = config.get('short_window', 5)
                                long_window = config.get('long_window', 20)
                            except:
                                # 如果找不到预设，尝试解析
                                short_window = 5
                                long_window = 20
                        else:
                            self.log_status(f"⚠️ 策略ID格式不正确: {strategy_id}")
                            continue

                        # 创建完整的策略显示名称
                        strategy_name = f"{base_name}({execution_name})"

                        strategy_info[strategy_id] = {
                            'name': strategy_name,
                            'file': pkl_file,
                            'valid_count': valid_count,
                            'params': {
                                'short_window': int(short_window),
                                'long_window': int(long_window),
                                'execution_price': execution_price
                            },
                            'format': 'pkl'
                        }
                        self.log_status(f"✅ 成功加载策略: {strategy_name}")
                        self.log_status(f"📊 有效股票数量: {valid_count}")
                        self.log_status(f"🔧 参数: MA({short_window},{long_window}), 执行价格: {execution_name}")

                    except Exception as e:
                        self.log_status(f"⚠️ 解析策略参数失败: {str(e)}")
                        self.log_status(f"⚠️ 策略ID: {strategy_id}")
                        continue
                else:
                    self.log_status(f"❌ 缓存数据格式不正确: 股票数据结构不符合预期")

            except Exception as e:
                self.log_status(f"⚠️ 分析缓存文件失败 {pkl_file}: {str(e)}")
                import traceback
                self.log_status(f"⚠️ 错误详情: {traceback.format_exc()}")

        # 存储策略信息并创建复选框
        self.strategy_info = strategy_info
        self._create_strategy_checkboxes(strategy_info)

    def load_cached_equity_curves_smart_align(self, source, strategy_name, num_stocks=50, min_data_length=3000):
        """智能对齐版本：按每天可用股票数动态计算平均收益率 - 支持CSV和PKL格式"""
        try:
            self.log_status(f"🔍 智能对齐加载: {strategy_name}")

            # 判断数据源类型
            if isinstance(source, str) and source.endswith('.pkl'):
                # PKL格式
                return self._load_pkl_equity_curves(source, strategy_name, num_stocks, min_data_length)
            else:
                # CSV格式（传入的是目录路径）
                return self._load_csv_equity_curves(source, strategy_name, num_stocks, min_data_length)

        except Exception as e:
            self.log_status(f"❌ {strategy_name}: 智能对齐失败 - {str(e)}")
            return [], None, None, None

    def _load_pkl_equity_curves(self, pkl_file, strategy_name, num_stocks, min_data_length):
        """加载PKL格式的收益率曲线"""
        cache_data = self.safe_load_pickle(pkl_file)
        if cache_data is None:
            return [], None, None, None

        valid_curves = []

        # 直接遍历股票数据
        for stock_code, result in cache_data.items():
            if (result and isinstance(result, dict) and
                'equity_curve' in result and result['equity_curve']):
                equity_curve = result['equity_curve']

                # 验证equity_curve有效性和最小长度要求
                if len(equity_curve) >= min_data_length:
                    first_point = equity_curve[0]
                    if isinstance(first_point, dict) and 'cumulative_return' in first_point:
                        valid_curves.append((stock_code, equity_curve))

        self.log_status(f"  📊 过滤后有效股票: {len(valid_curves)} (最小长度要求: {min_data_length})")

        if not valid_curves:
            return [], None, None, None

        # 随机选择指定数量的股票
        num_selected = min(num_stocks, len(valid_curves))
        selected_curves = random.sample(valid_curves, num_selected)

        self.log_status(f"  🎯 随机选择: {num_selected} 个股票")

        return self._process_equity_curves(selected_curves, strategy_name)

    def _load_csv_equity_curves(self, strategy_dir, strategy_name, num_stocks, min_data_length):
        """加载CSV格式的收益率曲线"""
        valid_curves = []

        # 获取所有股票的收益率曲线文件
        equity_files = glob.glob(os.path.join(strategy_dir, "*_equity.csv"))

        for equity_file in equity_files:
            try:
                stock_code = os.path.basename(equity_file).replace('_equity.csv', '')

                # 读取收益率曲线数据
                equity_df = pd.read_csv(equity_file, encoding='utf-8')
                if equity_df.empty:
                    continue

                # 转换日期列
                if 'date' in equity_df.columns:
                    equity_df['date'] = pd.to_datetime(equity_df['date'])

                # 验证最小长度要求
                if len(equity_df) >= min_data_length:
                    # 转换为字典格式以兼容原有逻辑
                    equity_curve = equity_df.to_dict('records')
                    valid_curves.append((stock_code, equity_curve))

            except Exception as e:
                self.log_status(f"  ⚠️ 读取 {stock_code} 收益曲线失败: {str(e)}")
                continue

        self.log_status(f"  📊 过滤后有效股票: {len(valid_curves)} (最小长度要求: {min_data_length})")

        if not valid_curves:
            return [], None, None, None

        # 随机选择指定数量的股票
        num_selected = min(num_stocks, len(valid_curves))
        selected_curves = random.sample(valid_curves, num_selected)

        self.log_status(f"  🎯 随机选择: {num_selected} 个股票")

        return self._process_equity_curves(selected_curves, strategy_name)

    def _process_equity_curves(self, selected_curves, strategy_name):
        """处理收益率曲线数据，进行智能对齐"""
        # 智能对齐：按日期构建所有数据
        date_returns_map = defaultdict(list)  # 日期 -> 当天所有股票的收益率列表
        all_dates = set()

        for stock_code, equity_curve in selected_curves:
            for point in equity_curve:
                if isinstance(point, dict) and 'date' in point and 'cumulative_return' in point:
                    date = point['date']
                    cumulative_return = float(point['cumulative_return'])

                    # 检查日期是否有效（不是NaT）
                    if pd.notna(date):
                        date_returns_map[date].append(cumulative_return)
                        all_dates.add(date)

        # 按日期排序，过滤掉无效日期
        valid_dates = [d for d in all_dates if pd.notna(d)]
        sorted_dates = sorted(valid_dates)

        if not sorted_dates:
            self.log_status(f"❌ {strategy_name}: 没有有效的日期数据")
            return [], None, None, None

        # 安全地格式化日期
        try:
            start_date_str = sorted_dates[0].strftime('%Y-%m-%d')
            end_date_str = sorted_dates[-1].strftime('%Y-%m-%d')
            self.log_status(f"  📅 日期范围: {start_date_str} 到 {end_date_str}")
        except (AttributeError, ValueError):
            self.log_status(f"  📅 日期范围: {sorted_dates[0]} 到 {sorted_dates[-1]}")

        self.log_status(f"  📅 总交易日: {len(sorted_dates)}")

        # 计算每天的平均收益率
        daily_avg_returns = []
        daily_stock_counts = []

        for date in sorted_dates:
            returns_today = date_returns_map[date]
            if returns_today:
                avg_return = np.mean(returns_today)
                daily_avg_returns.append(avg_return)
                daily_stock_counts.append(len(returns_today))
            else:
                # 如果某天没有数据，使用前一天的收益率
                if daily_avg_returns:
                    daily_avg_returns.append(daily_avg_returns[-1])
                    daily_stock_counts.append(0)
                else:
                    daily_avg_returns.append(0.0)
                    daily_stock_counts.append(0)

        # 统计信息
        min_stocks = min(daily_stock_counts) if daily_stock_counts else 0
        max_stocks = max(daily_stock_counts) if daily_stock_counts else 0
        avg_stocks = np.mean(daily_stock_counts) if daily_stock_counts else 0

        self.log_status(f"  📈 每日股票数: 最少{min_stocks}, 最多{max_stocks}, 平均{avg_stocks:.1f}")
        self.log_status(f"  📈 智能对齐完成: {len(daily_avg_returns)} 个数据点")
        self.log_status(f"  📈 最终收益率: {daily_avg_returns[-1]:.2%}")

        return [s[0] for s in selected_curves], daily_avg_returns, sorted_dates, daily_stock_counts

    def get_benchmark_data_aligned(self, dates):
        """获取与策略时间对齐的基准数据"""
        try:
            # 首先尝试新的数据源：日线数据/SH000001.txt
            benchmark_file = "../日线数据/SH000001.txt"

            # 详细检查文件状态
            self.log_status(f"🔍 检查基准文件路径: {benchmark_file}")
            self.log_status(f"🔍 文件绝对路径: {os.path.abspath(benchmark_file)}")
            self.log_status(f"🔍 文件是否存在: {os.path.exists(benchmark_file)}")
            if os.path.exists(benchmark_file):
                file_size = os.path.getsize(benchmark_file)
                self.log_status(f"🔍 文件大小: {file_size} 字节")

            if os.path.exists(benchmark_file):
                self.log_status(f"📊 使用上证指数数据: {benchmark_file}")
                # 依次尝试多种编码
                encodings_to_try = ['utf-8', 'gbk', 'gb2312', 'ansi', 'mbcs']
                benchmark_df = None
                for enc in encodings_to_try:
                    try:
                        self.log_status(f"🔄 尝试编码: {enc}")
                        benchmark_df = pd.read_csv(benchmark_file, encoding=enc)
                        self.log_status(f"✅ 成功用编码 {enc} 读取基准数据")
                        self.log_status(f"📊 数据形状: {benchmark_df.shape}")
                        self.log_status(f"📊 列名: {list(benchmark_df.columns)}")
                        break
                    except Exception as e:
                        self.log_status(f"⚠️ 用编码 {enc} 读取失败: {str(e)}")
                        import traceback
                        self.log_status(f"⚠️ 详细错误: {traceback.format_exc()}")
                        benchmark_df = None

                if benchmark_df is None:
                    self.log_status("❌ 所有常用编码均无法读取基准数据文件，请检查文件编码！")
                    import tkinter.messagebox as messagebox
                    messagebox.showerror("基准数据读取失败", "所有常用编码均无法读取基准数据文件，请用记事本另存为UTF-8或GBK格式！")
                    return None, None

                # 检查列名并重命名
                if len(benchmark_df.columns) >= 5:
                    # 假设列顺序为：日期,开盘,最高,最低,收盘,成交量
                    benchmark_df.columns = ['日期', '开盘', '最高', '最低', '收盘', '成交量']

                    # 转换日期格式
                    benchmark_df['日期'] = pd.to_datetime(benchmark_df['日期'], format='%Y/%m/%d')
                    benchmark_df = benchmark_df.set_index('日期').sort_index()

                    self.log_status(f"✅ 成功读取上证指数数据: {len(benchmark_df)} 条记录")

                    # 安全地格式化日期
                    try:
                        min_date = benchmark_df.index.min()
                        max_date = benchmark_df.index.max()
                        if pd.notna(min_date) and pd.notna(max_date):
                            self.log_status(f"📅 数据范围: {min_date.strftime('%Y-%m-%d')} 到 {max_date.strftime('%Y-%m-%d')}")
                        else:
                            self.log_status(f"📅 数据范围: {min_date} 到 {max_date}")
                    except (AttributeError, ValueError):
                        self.log_status(f"📅 数据范围: {benchmark_df.index.min()} 到 {benchmark_df.index.max()}")

                else:
                    raise ValueError("数据文件格式不正确")

            else:
                # 回退到原有的数据源
                benchmark_file = "基准指数数据/上证指数_日K线.csv"

                if not os.path.exists(benchmark_file):
                    self.log_status("❌ 基准文件不存在，跳过基准对比")
                    return None, None

                self.log_status(f"📊 使用备用基准数据: {benchmark_file}")

                # 读取基准数据
                benchmark_df = pd.read_csv(benchmark_file)
                benchmark_df['日期'] = pd.to_datetime(benchmark_df['日期'])
                benchmark_df = benchmark_df.set_index('日期').sort_index()

            # 将策略日期转换为相同格式
            strategy_dates = pd.to_datetime(dates)

            # 筛选基准数据以匹配策略日期范围
            start_date = strategy_dates.min()
            end_date = strategy_dates.max()

            # 安全地格式化日期
            try:
                if pd.notna(start_date) and pd.notna(end_date):
                    self.log_status(f"📅 基准数据对齐: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
                else:
                    self.log_status(f"📅 基准数据对齐: {start_date} 到 {end_date}")
            except (AttributeError, ValueError):
                self.log_status(f"📅 基准数据对齐: {start_date} 到 {end_date}")

            # 过滤基准数据
            benchmark_subset = benchmark_df[(benchmark_df.index >= start_date) &
                                           (benchmark_df.index <= end_date)]

            if len(benchmark_subset) == 0:
                self.log_status("❌ 没有找到匹配的基准数据")
                return None, None

            # 计算累积收益率
            initial_close = benchmark_subset['收盘'].iloc[0]
            benchmark_returns = (benchmark_subset['收盘'] / initial_close - 1).values

            self.log_status(f"✅ 基准数据: {len(benchmark_returns)} 个点, 总收益: {benchmark_returns[-1]:.2%}")

            return benchmark_subset.index.tolist(), benchmark_returns

        except Exception as e:
            self.log_status(f"❌ 基准数据处理失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return None, None

    def start_analysis(self):
        """开始智能对齐分析"""
        # 获取选中的策略
        selected_strategies = []
        for strategy_id, var in self.strategy_vars.items():
            if var.get():
                selected_strategies.append(strategy_id)

        if not selected_strategies:
            messagebox.showwarning("警告", "请至少选择一个策略进行分析")
            return

        # 获取参数
        try:
            num_stocks = int(self.num_stocks_var.get())
            min_data_length = int(self.min_data_length_var.get())
            seed = int(self.seed_var.get())
        except ValueError:
            messagebox.showerror("错误", "请输入有效的数字参数")
            return

        # 清空状态
        self.status_text.delete(1.0, tk.END)
        self.log_status(f"📊 开始智能对齐多策略分析...")
        self.log_status(f"🎯 选中策略: {len(selected_strategies)} 个")
        self.log_status(f"🎯 每策略股票数: {num_stocks}")
        self.log_status(f"🎯 最小数据长度: {min_data_length} 天")
        self.log_status(f"💡 使用智能对齐：每天按可用股票数动态平均")

        # 强制勾选"包含上证指数基准"
        self.include_benchmark_var.set(True)

        # 在后台线程中运行分析
        analysis_thread = threading.Thread(
            target=self._run_analysis,
            args=(selected_strategies, num_stocks, min_data_length, seed)
        )
        analysis_thread.daemon = True
        analysis_thread.start()

    def _run_analysis(self, selected_strategies, num_stocks, min_data_length, seed):
        """在后台运行智能对齐分析"""
        try:
            # 设置随机种子
            random.seed(seed)
            np.random.seed(seed)

            # 策略定义映射
            strategy_names = {}
            for strategy_id in selected_strategies:
                # 用唯一名，包含执行价格
                if hasattr(self, 'strategy_info') and strategy_id in self.strategy_info:
                    strategy_names[strategy_id] = self.strategy_info[strategy_id]['name']
                else:
                    strategy_names[strategy_id] = f"策略_{strategy_id}"

            cache_dir = "./cache"

            # 存储所有策略数据
            self.strategy_data = {}
            common_dates = None

            # 加载所有策略的智能对齐数据
            for strategy_id in selected_strategies:
                strategy_name = strategy_names.get(strategy_id, f"策略_{strategy_id}")

                # 获取策略信息
                strategy_info = self.strategy_info.get(strategy_id, {})
                strategy_format = strategy_info.get('format', 'pkl')

                if strategy_format == 'csv':
                    # CSV格式：使用目录路径
                    strategy_source = strategy_info.get('dir')
                    if not strategy_source or not os.path.exists(strategy_source):
                        self.log_status(f"⚠️ CSV策略目录不存在: {strategy_source}")
                        continue
                else:
                    # PKL格式：使用文件路径
                    strategy_source = os.path.join(cache_dir, f"strategy_cache_{strategy_id}.pkl")
                    if not os.path.exists(strategy_source):
                        self.log_status(f"⚠️ PKL策略文件不存在: {strategy_source}")
                        continue

                stocks, avg_curve, dates, daily_counts = self.load_cached_equity_curves_smart_align(
                    strategy_source, strategy_name, num_stocks, min_data_length)

                if avg_curve is not None:
                    self.strategy_data[strategy_name] = {
                        'returns': avg_curve,
                        'stocks': stocks,
                        'dates': dates,
                        'daily_counts': daily_counts
                    }

                    # 使用最长的日期序列作为公共基准
                    if common_dates is None or len(dates) > len(common_dates):
                        common_dates = dates

                    self.log_status(f"✅ {strategy_name}: 成功加载")
                else:
                    self.log_status(f"❌ {strategy_name}: 加载失败")

            if not self.strategy_data:
                self.log_status("❌ 没有可用的策略数据")
                return

            # 获取对齐的基准数据
            benchmark_dates, benchmark_returns = None, None
            if self.include_benchmark_var.get():
                benchmark_dates, benchmark_returns = self.get_benchmark_data_aligned(common_dates)

            # 绘制结果
            self._plot_results(common_dates, benchmark_returns, num_stocks, min_data_length)

            # 显示详细统计
            self._show_statistics()

        except Exception as e:
            self.log_status(f"❌ 分析过程出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def _plot_results(self, common_dates, benchmark_returns, num_stocks, min_data_length):
        """绘制智能对齐分析结果"""
        try:
            # 清空图表
            self.ax.clear()

            # 定义更丰富的颜色调色板（避免灰色）
            colors = [
                '#1f77b4',  # 蓝色
                '#ff7f0e',  # 橙色
                '#2ca02c',  # 绿色
                '#d62728',  # 红色
                '#9467bd',  # 紫色
                '#8c564b',  # 棕色
                '#e377c2',  # 粉色
                '#7f7f7f',  # 灰色（备用）
                '#bcbd22',  # 橄榄色
                '#17becf',  # 青色
                '#ff9896',  # 浅红色
                '#98df8a',  # 浅绿色
                '#c5b0d5',  # 浅紫色
                '#c49c94',  # 浅棕色
                '#f7b6d3'   # 浅粉色
            ]

            # 绘制策略曲线（线条更细）
            for i, (strategy_name, data) in enumerate(self.strategy_data.items()):
                returns = data['returns']
                returns_pct = [r * 100 for r in returns]  # 转换为百分比

                self.ax.plot(range(len(returns)), returns_pct,
                            label=f"{strategy_name} ({len(data['stocks'])}只股票智能平均)",
                            color=colors[i % len(colors)], linewidth=1.5, alpha=0.9)

            # 绘制基准线
            if benchmark_returns is not None:
                self.log_status(f"[DEBUG] 基准数据长度: {len(benchmark_returns)}")
                if len(benchmark_returns) == 0:
                    self.log_status("❌ 基准数据为空，无法绘制上证指数曲线！")
                    import tkinter.messagebox as messagebox
                    messagebox.showwarning("基准数据缺失", "基准数据为空，无法绘制上证指数曲线！\n请检查基准数据文件和日期范围是否匹配。")
                else:
                    benchmark_pct = [r * 100 for r in benchmark_returns]
                    # 将基准数据对齐到策略长度
                    strategy_length = len(list(self.strategy_data.values())[0]['returns'])
                    if len(benchmark_pct) != strategy_length:
                        # 基准数据插值或截取
                        if len(benchmark_pct) > strategy_length:
                            indices = np.linspace(0, len(benchmark_pct)-1, strategy_length, dtype=int)
                            benchmark_pct = [benchmark_pct[i] for i in indices]
                        else:
                            try:
                                from scipy.interpolate import interp1d
                                x_old = np.linspace(0, 1, len(benchmark_pct))
                                x_new = np.linspace(0, 1, strategy_length)
                                f = interp1d(x_old, benchmark_pct, kind='linear')
                                benchmark_pct = f(x_new).tolist()
                            except ImportError:
                                benchmark_pct = np.interp(
                                    np.linspace(0, len(benchmark_pct)-1, strategy_length),
                                    np.arange(len(benchmark_pct)),
                                    benchmark_pct
                                ).tolist()
                    self.ax.plot(range(len(benchmark_pct)), benchmark_pct,
                                label=f"上证指数基准 ({benchmark_returns[-1]:.2%})",
                                color='#666666', linewidth=1.5, alpha=0.9, linestyle='-')
                    self.log_status("✅ 上证指数基准线已绘制")
            else:
                self.log_status("❌ benchmark_returns为None，未能绘制上证指数曲线！")
                import tkinter.messagebox as messagebox
                messagebox.showwarning("基准数据缺失", "未能获取到上证指数基准数据，无法绘制基准曲线！\n请检查基准数据文件是否存在且格式正确。")

            # 设置图表属性
            self.ax.set_title(f'双均线策略多策略对比 - 智能对齐版本\n'
                             f'(每策略{num_stocks}只股票, 最小{min_data_length}天数据, 动态对齐)',
                             fontsize=14, fontweight='bold', pad=20)
            self.ax.set_xlabel('交易日', fontsize=12)
            self.ax.set_ylabel('累积收益率 (%)', fontsize=12)
            self.ax.legend(fontsize=10, loc='upper left')
            self.ax.grid(True, alpha=0.3)

            # 设置Y轴格式
            self.ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.0f}%'))

            # 改进X轴日期显示逻辑
            if common_dates and len(common_dates) > 0:
                total_days = len(common_dates)

                # 根据数据长度动态调整刻度数量
                if total_days <= 500:
                    tick_count = 8
                elif total_days <= 1000:
                    tick_count = 10
                elif total_days <= 2000:
                    tick_count = 12
                else:
                    tick_count = 15

                # 计算刻度间隔，确保不重复
                step = max(1, total_days // tick_count)
                x_ticks = list(range(0, total_days, step))

                # 确保包含最后一个点
                if x_ticks[-1] != total_days - 1:
                    x_ticks.append(total_days - 1)

                # 生成日期标签，避免重复
                x_labels = []
                used_labels = set()

                for i, tick in enumerate(x_ticks):
                    if tick < len(common_dates):
                        date = common_dates[tick]
                        if hasattr(date, 'strftime'):
                            # 根据数据跨度选择合适的日期格式
                            if total_days > 2000:  # 超过8年数据，只显示年份
                                label = date.strftime('%Y')
                            elif total_days > 500:  # 2-8年数据，显示年-月
                                label = date.strftime('%Y-%m')
                            else:  # 少于2年数据，显示年-月-日
                                label = date.strftime('%Y-%m-%d')
                        else:
                            label = str(date)[:10]

                        # 智能去重：如果标签重复，则跳过显示（用空字符串）
                        if label not in used_labels:
                            x_labels.append(label)
                            used_labels.add(label)
                        else:
                            # 重复标签用空字符串替代，但保留首尾标签
                            if i == 0 or i == len(x_ticks) - 1:
                                # 首尾标签强制显示，添加月份信息区分
                                if total_days > 2000:
                                    detailed_label = date.strftime('%Y-%m')
                                    if detailed_label not in used_labels:
                                        x_labels.append(detailed_label)
                                        used_labels.add(detailed_label)
                                    else:
                                        x_labels.append("")
                                else:
                                    x_labels.append(label)
                            else:
                                x_labels.append("")  # 中间重复的标签用空字符串
                    else:
                        x_labels.append("")

                # 设置X轴刻度和标签
                self.ax.set_xticks(x_ticks)
                self.ax.set_xticklabels(x_labels, rotation=45, ha='right')

                # 调整布局以防止标签被截断
                plt.setp(self.ax.xaxis.get_majorticklabels(), rotation=45, ha='right')

            # 调整图表布局
            self.fig.tight_layout()

            # 刷新画布
            self.canvas.draw()

            self.log_status("✅ 智能对齐图表绘制完成")

        except Exception as e:
            self.log_status(f"❌ 绘图失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def _show_statistics(self):
        """显示详细统计信息"""
        self.log_status(f"\n📊 智能对齐策略表现总结:")
        best_strategy = None
        best_return = -float('inf')

        for strategy_name, data in self.strategy_data.items():
            returns = data['returns']
            final_return = returns[-1]
            max_return = max(returns)
            drawdown = min(returns)
            volatility = np.std(np.diff(returns))
            sharpe_ratio = final_return / volatility if volatility > 0 else 0

            self.log_status(f"  {strategy_name}:")
            self.log_status(f"    📈 最终收益: {final_return:.2%}")
            self.log_status(f"    📈 最大收益: {max_return:.2%}")
            self.log_status(f"    📉 最大回撤: {drawdown:.2%}")
            self.log_status(f"    📊 波动率: {volatility:.4f}")
            self.log_status(f"    📊 夏普比率: {sharpe_ratio:.2f}")
            self.log_status(f"    📊 数据覆盖: {len(data['dates'])} 交易日")
            self.log_status(f"    📊 平均每日股票数: {np.mean(data['daily_counts']):.1f}")

            if final_return > best_return:
                best_return = final_return
                best_strategy = strategy_name

        self.log_status(f"\n🏆 最佳策略: {best_strategy} ({best_return:.2%})")

    def save_chart(self):
        """保存图表"""
        if not self.strategy_data:
            messagebox.showwarning("警告", "请先运行分析")
            return

        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = filedialog.asksaveasfilename(
                defaultextension=".png",
                filetypes=[("PNG files", "*.png"), ("All files", "*.*")],
                initialname=f"多策略对比_智能对齐_{timestamp}.png"
            )

            if filename:
                self.fig.savefig(filename, dpi=300, bbox_inches='tight')
                self.log_status(f"✅ 图表已保存: {filename}")
                messagebox.showinfo("成功", f"图表已保存到:\n{filename}")

        except Exception as e:
            self.log_status(f"❌ 保存图表失败: {str(e)}")
            messagebox.showerror("错误", f"保存图表失败:\n{str(e)}")

    def export_data(self):
        """导出数据"""
        if not self.strategy_data:
            messagebox.showwarning("警告", "请先运行分析")
            return

        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                initialname=f"多策略对比数据_智能对齐_{timestamp}.csv"
            )

            if filename:
                # 准备导出数据
                export_data = {}

                # 获取最长的日期序列
                max_length = max(len(data['returns']) for data in self.strategy_data.values())

                for strategy_name, data in self.strategy_data.items():
                    returns = data['returns']
                    dates = data['dates']

                    # 如果长度不够，用最后一个值填充
                    if len(returns) < max_length:
                        returns = returns + [returns[-1]] * (max_length - len(returns))
                        dates = dates + [dates[-1]] * (max_length - len(dates))

                    export_data[f'{strategy_name}_收益率'] = [f"{r:.4f}" for r in returns]
                    export_data[f'{strategy_name}_日期'] = [d.strftime('%Y-%m-%d') if hasattr(d, 'strftime') else str(d) for d in dates]

                # 创建DataFrame并保存
                df = pd.DataFrame(export_data)
                df.to_csv(filename, index=False, encoding='utf-8-sig')

                self.log_status(f"✅ 数据已导出: {filename}")
                messagebox.showinfo("成功", f"数据已导出到:\n{filename}")

        except Exception as e:
            self.log_status(f"❌ 导出数据失败: {str(e)}")
            messagebox.showerror("错误", f"导出数据失败:\n{str(e)}")


# 测试代码
if __name__ == "__main__":
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    app = MultiStrategyComparisonWindow(root)
    root.mainloop()